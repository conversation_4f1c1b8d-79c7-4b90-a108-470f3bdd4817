<?php

namespace models\composite\oDrawManagement;

use models\types\strongType;
use models\standard\Strings;
use models\standard\Dates;

class DrawSummaryManager extends strongType
{
    private ?int $LMRId = null;
    private ?DrawRequestManager $drawRequestManager = null;
    public ?array $drawRequestHistory = null;

    // Static public properties for all summary data
    public static ?string $address = null;
    public static ?string $city = null;
    public static ?string $state = null;
    public static ?string $zip = null;
    public static float $initialLoan = 0;
    public static float $rehabCostFinanced = 0;
    public static float $totalLoanAmount = 0;
    public static float $currentLoanBalance = 0;
    public static float $rehabCost = 0;
    public static ?string $arv = null;
    public static float $totalDrawsFunded = 0;
    public static float $holdbackRemaining = 0;
    public static ?string $closingDate = null;
    public static ?string $maturityDate = null;
    public static ?string $dateOfLastDraw = null;
    public static ?string $dateOfCurrentDraw = null;

    public function __construct(int $LMRId, ?DrawRequestManager $drawRequestManager = null)
    {
        $this->LMRId = $LMRId;
        if (!$drawRequestManager) {
            $drawRequestManager = DrawRequestManager::forLoanFile($LMRId);
        }
        $this->drawRequestManager = $drawRequestManager;
        $this->drawRequestHistory = $drawRequestManager->getDrawRequestHistory();

        $this->loadSummaryData();
    }

    /**
     * Static initializer method that takes constructor arguments
     * @param int $LMRId
     * @param DrawRequestManager|null $drawRequestManager
     * @return self
     */
    public static function initialize(int $LMRId, ?DrawRequestManager $drawRequestManager = null): self
    {
        return new self($LMRId, $drawRequestManager);
    }

    /**
     * Load all summary data and populate static properties
     * @return void
     */
    private function loadSummaryData(): void
    {
        // Property information
        self::$address = $this->safeShowField('propertyAddress', 'LMRInfo');
        self::$city = $this->safeShowField('propertyCity', 'LMRInfo');
        self::$state = $this->safeShowField('propertyState', 'LMRInfo');
        self::$zip = $this->safeShowField('propertyZip', 'LMRInfo');

        // Loan amounts
        self::$initialLoan = $this->safeShowFieldFloat('InitialLoanAmount', 'fileCalculatedValues');
        self::$rehabCostFinanced = $this->safeShowFieldFloat('rehabCostFinanced', 'fileHMLONewLoanInfo');
        self::$totalLoanAmount = $this->safeShowFieldFloat('totalLoanAmount', 'fileHMLONewLoanInfo');
        self::$currentLoanBalance = $this->safeShowFieldFloat('CurrentLoanBalance', 'fileCalculatedValues');
        self::$rehabCost = $this->safeShowFieldFloat('rehabCost', 'fileHMLOInfo');

        // ARV with percentage
        $arvValue = $this->safeShowFieldFloat('FullARV', 'fileCalculatedValues');
        self::$arv = $arvValue > 0 ? $arvValue . '%' : '-';

        // Date information
        self::$closingDate = $this->safeFormatDate($this->safeShowField('closingDate', 'QAInfo'));
        self::$maturityDate = $this->safeFormatDate($this->safeShowField('maturityDate', 'fileHMLOPropertyInfo'));

        // Calculate draw-related data
        $this->calculateDrawData();
    }

    /**
     * Calculate draw-related data from history
     * @return void
     */
    private function calculateDrawData(): void
    {
        if (empty($this->drawRequestHistory)) {
            self::$totalDrawsFunded = 0;
            self::$holdbackRemaining = self::$rehabCostFinanced;
            self::$dateOfLastDraw = '-';
            self::$dateOfCurrentDraw = '-';
            return;
        }

        // Calculate total approved draws
        $totalApproved = array_column($this->drawRequestHistory, 'approvedAmount');
        self::$totalDrawsFunded = array_sum($totalApproved);

        // Calculate holdback remaining
        self::$holdbackRemaining = self::$rehabCostFinanced > 0 ? self::$rehabCostFinanced - self::$totalDrawsFunded : 0;

        // Find last draw request and last approved draw request
        $lastDrawRequest = end($this->drawRequestHistory);
        $lastApprovedDrawRequest = null;

        // Find the latest approved draw request
        foreach (array_reverse($this->drawRequestHistory) as $history) {
            if ($history['status'] === 'approved') {
                $lastApprovedDrawRequest = $history;
                break;
            }
        }

        // Set draw dates
        self::$dateOfCurrentDraw = '-';
        self::$dateOfLastDraw = '-';

        if ($lastDrawRequest && $lastDrawRequest['status'] !== 'approved' && !empty($lastDrawRequest['submittedAt'])) {
            self::$dateOfCurrentDraw = $this->safeFormatDate($lastDrawRequest['submittedAt']);
        }

        if ($lastApprovedDrawRequest && !empty($lastApprovedDrawRequest['approvedAt'])) {
            // Check if this is a draw request (assuming we have access to requestData)
            global $requestData;
            if (isset($requestData['isDrawRequest']) && $requestData['isDrawRequest']) {
                self::$dateOfLastDraw = $this->safeFormatDate($lastApprovedDrawRequest['approvedAt']);
            }
        }
    }

    /**
     * Safely get field value with fallback
     * @param string $fieldName
     * @param string $tableName
     * @return string
     */
    private function safeShowField(string $fieldName, string $tableName): string
    {
        try {
            $value = Strings::showField($fieldName, $tableName);
            return !empty(trim($value)) ? trim($value) : '';
        } catch (\Exception $e) {
            // Log error if needed in the future
            return '';
        }
    }

    /**
     * Safely get numeric field value with fallback to 0
     * @param string $fieldName
     * @param string $tableName
     * @return float
     */
    private function safeShowFieldFloat(string $fieldName, string $tableName): float
    {
        try {
            $value = Strings::showField($fieldName, $tableName);
            return !empty(trim($value)) ? (float)Strings::Numeric(trim($value)) : 0;
        } catch (\Exception $e) {
            // Log error if needed in the future
            return 0;
        }
    }

    /**
     * Safely format date with fallback to '-'
     * @param string|null $date
     * @return string
     */
    private function safeFormatDate(?string $date): string
    {
        if (empty($date) || Dates::IsEmpty($date)) {
            return '-';
        }

        try {
            $formatted = Dates::formatDateWithRE($date, 'YMD', 'm/d/Y');
            return !empty($formatted) ? $formatted : '-';
        } catch (\Exception $e) {
            // Log error if needed in the future
            return '-';
        }
    }

    /**
     * Get formatted total draws funded for display
     * @return string
     */
    public static function getFormattedTotalDrawsFunded(): string
    {
        return number_format(self::$totalDrawsFunded);
    }

    /**
     * Get formatted holdback remaining for display
     * @return string
     */
    public static function getFormattedHoldbackRemaining(): string
    {
        return number_format(self::$holdbackRemaining);
    }

    /**
     * Get all summary data as an array
     * @return array
     */
    public static function getAllData(): array
    {
        return [
            'address' => self::$address,
            'city' => self::$city,
            'state' => self::$state,
            'zip' => self::$zip,
            'initialLoan' => self::$initialLoan,
            'rehabCostFinanced' => self::$rehabCostFinanced,
            'totalLoanAmount' => self::$totalLoanAmount,
            'currentLoanBalance' => self::$currentLoanBalance,
            'rehabCost' => self::$rehabCost,
            'arv' => self::$arv,
            'totalDrawsFunded' => self::$totalDrawsFunded,
            'holdbackRemaining' => self::$holdbackRemaining,
            'closingDate' => self::$closingDate,
            'maturityDate' => self::$maturityDate,
            'dateOfLastDraw' => self::$dateOfLastDraw,
            'dateOfCurrentDraw' => self::$dateOfCurrentDraw,
        ];
    }
}
