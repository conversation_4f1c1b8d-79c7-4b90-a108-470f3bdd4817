<?php
namespace models\composite\oDrawManagement;

use models\lendingwise\tblProcessingCompanyDrawTemplateSettings;
use models\composite\oDrawManagement\SowCategory;
use models\lendingwise\tblDrawTemplateCategories;
use models\lendingwise\db\tblDrawTemplateCategories_db;
use models\types\strongType;

class SowTemplate extends strongType
{
    /**
     * @var int|null The ID of the template.
     */
    public ?int $id = null;

    /**
     * @var string|null The PC ID associated with the template.
     */
    public ?string $pcId = null;

    public ?int $allowBorrowersAddEditCategories = null;
    public ?int $allowBorrowersDeleteCategories = null;
    public ?int $allowBorrowersAddEditLineItems = null;
    public ?int $allowBorrowersDeleteLineItems = null;
    public ?int $allowBorrowersSOWRevisions = null;
    public ?int $allowBorrowersExceedFinancedRehabCostOnRevision = null;
    public ?float $drawFee = null;

    /**
     * @var tblProcessingCompanyDrawTemplateSettings|null The database table object for the template.
     */
    public ?tblProcessingCompanyDrawTemplateSettings $template = null;

    /**
     * @var SowCategory[] An array of SowCategory objects, indexed by category ID.
     */
    public array $categories = [];

    /**
     * SowTemplate constructor.
     * @param tblProcessingCompanyDrawTemplateSettings|null $template The database template object to initialize from.
     */
    public function __construct(?tblProcessingCompanyDrawTemplateSettings $template = null) {
        $this->id = $template->id;
        $this->pcId = $template->PCID;
        $this->template = $template;
        $this->allowBorrowersAddEditCategories = $template->allowBorrowersAddEditCategories;
        $this->allowBorrowersDeleteCategories = $template->allowBorrowersDeleteCategories;
        $this->allowBorrowersAddEditLineItems = $template->allowBorrowersAddEditLineItems;
        $this->allowBorrowersDeleteLineItems = $template->allowBorrowersDeleteLineItems;
        $this->allowBorrowersSOWRevisions = $template->allowBorrowersSOWRevisions;
        $this->allowBorrowersExceedFinancedRehabCostOnRevision = $template->allowBorrowersExceedFinancedRehabCostOnRevision;
        $this->drawFee = $template->drawFee;
        if ($template->id) $this->loadCategories();
    }
    /**
     * Load categories for a given template ID
     * @param int $templateId Template ID to load categories for
     * @return array Returns an array of SowCategory objects or an empty array
     */
    private function loadCategories(): void
    {
        $categoriesData = tblDrawTemplateCategories::GetAll(
            [tblDrawTemplateCategories_db::COLUMN_TEMPLATEID => $this->id],
            [tblDrawTemplateCategories_db::COLUMN_ORDER => 'ASC']
        );

        foreach ($categoriesData as $tblCategoryObj) {
            $this->addCategory(new SowCategory($tblCategoryObj));
        }
        $this->sortCategories();
    }

    /**
     * Adds a SowCategory object to the template's categories.
     * @param SowCategory $category The category object to add.
     * @return void
     */
    public function addCategory(SowCategory $category): void {
        $this->categories[$category->id] = $category;
    }

    /**
     * Sorts the categories by their order property.
     * @return void
     */
    private function sortCategories(): void {
        uasort($this->categories, function($a, $b) {
            return $a->order <=> $b->order;
        });
    }

    /**
     * Save template settings for a processing company
     * @param array $data Form data containing template settings
     * @return array Result array with success status and message
     */
    public function save(array $data): void
    {
            $this->template->allowBorrowersAddEditCategories = intval($data['allowBorrowersAddEditCategories'] ?? 0);
            $this->template->allowBorrowersDeleteCategories = intval($data['allowBorrowersDeleteCategories'] ?? 0);
            $this->template->allowBorrowersAddEditLineItems = intval($data['allowBorrowersAddEditLineItems'] ?? 0);
            $this->template->allowBorrowersDeleteLineItems = intval($data['allowBorrowersDeleteLineItems'] ?? 0);
            $this->template->allowBorrowersSOWRevisions = intval($data['allowBorrowersSOWRevisions'] ?? 0);
            $this->template->allowBorrowersExceedFinancedRehabCostOnRevision = intval($data['allowBorrowersExceedFinancedRehabCostOnRevision'] ?? 0);
            $drawFee = floatval($data['drawFee'] ?? 0);
            $this->template->drawFee = $drawFee;

            $this->template->Save();
    }

    /**
     * Retrieves all categories associated with this template.
     * @return SowCategory[] An array of SowCategory objects.
     */
    public function getAllCategories(): array {
        return $this->categories;
    }

    /**
     * Retrieves a specific category by its ID.
     * @param int $categoryId The ID of the category to retrieve.
     * @return SowCategory|null The SowCategory object if found, otherwise null.
     */
    public function getCategoryById(int $categoryId): ?SowCategory {
        return $this->categories[$categoryId] ?? null;
    }

    /**
     * Deletes categories and their associated line items from the template.
     *
     * @param array $categoriesIdsToDelete An array of category IDs to delete.
     * @return void
     */
    public function deleteCategories(array $categoriesIdsToDelete): void {
        if (empty($categoriesIdsToDelete)) return;

        foreach ($categoriesIdsToDelete as $categoryId) {
            if(isset($this->categories[$categoryId])) {
                foreach($this->categories[$categoryId]->lineItems as $lineItem) {
                    $lineItem->delete();
                }
                $this->categories[$categoryId]->delete();
                unset($this->categories[$categoryId]);
            }
        }
    }

    /**
     * Deletes specific line items from the template across all categories.
     *
     * @param array $lineItemIdsToDelete An array of line item IDs to delete.
     * @return void
     */
    public function deleteLineItems(array $lineItemIdsToDelete): void {
        if (empty($lineItemIdsToDelete)) return;

        $idsToDelete = array_flip($lineItemIdsToDelete);

        foreach ($this->categories as $category) {
            foreach ($category->lineItems as $lineItemId => $lineItem) {
                if (isset($idsToDelete[$lineItemId])) {
                    $lineItem->delete();
                    unset($category->lineItems[$lineItemId]);
                    unset($idsToDelete[$lineItemId]);
                    if (empty($idsToDelete)) {
                        return;
                    }
                }
            }
        }
    }
}
