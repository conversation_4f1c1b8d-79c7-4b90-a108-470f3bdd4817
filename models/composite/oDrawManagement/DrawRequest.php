<?php
namespace models\composite\oDrawManagement;

use models\types\strongType;
use models\composite\oDrawManagement\traits\PropertiesMapper;
use models\lendingwise\tblDrawRequests;
use models\lendingwise\db\tblDrawRequestCategories_db;
use models\lendingwise\tblDrawRequestCategories;
use models\composite\oDrawManagement\BorrowerDrawCategory;
use models\composite\oDrawManagement\BorrowerDrawLineItem;
use models\standard\Dates;

class DrawRequest extends strongType
{
    use PropertiesMapper;

    /**
     * Status constants
     */
    public const STATUS_NEW = 'new';
    public const STATUS_PENDING = 'pending';
    public const STATUS_APPROVED = 'approved';
    public const STATUS_REJECTED = 'rejected';

    /**
     * @var int|null The ID of the borrower draw request.
     */
    public ?int $id = null;

    /**
     * @var int|null The LMR ID associated with the draw request.
     */
    public ?int $LMRId = null;

    /**
     * @var string The status of the draw request.
     */
    public string $status = self::STATUS_PENDING;

    /**
     * @var int|null The draw request flag.
     */

    public ?int $sowApproved = null;

    /**
     * @var string|null The submission date of the draw request.
     * @var integer|null
     */
    public ?int $isDrawRequest = null;

    /**
     * @var string|null The last update date.
     */
    public ?string $updatedAt = null;

    /**
     * @var tblDrawRequests|null The database table object for the draw request.
     */
    public ?tblDrawRequests $drawRequest = null;

    /**
     * @var array|null An array of categories associated with the draw request.
     */
    public ?array $categories = null;

    /**
     * DrawRequest constructor.
     * @param tblDrawRequests|null $drawRequest The database draw request object to initialize from.
     */
    public function __construct(?tblDrawRequests $drawRequest = null) {
        if ($drawRequest == null) $drawRequest = new tblDrawRequests();
        $this->setProperties($drawRequest);
        if ($drawRequest->id) $this->loadCategories();
    }

    /**
     * Saves the current draw request object to the database.
     * @return array The result of the save operation.
     */
    public function save(?array $drawRequestData = null): array {
        if(!empty($drawRequestData)) $this->setFromArray($drawRequestData);
        $saved = $this->drawRequest->save();
        $this->id = $this->drawRequest->id;
        return $saved;
    }

    public function getDbObject(): tblDrawRequests {
        return $this->drawRequest;
    }

    /**
     * Sets the properties of the draw request from an associative array.
     * @param array $drawRequestData Associative array containing draw request data.
     * @return void
     */
    private function setFromArray(array $drawRequestData): void {
        $this->drawRequest->id = $drawRequestData['id'] ?? null;
        $this->drawRequest->LMRId = $drawRequestData['LMRId'];
        $this->drawRequest->status = $drawRequestData['status'] ?? self::STATUS_NEW;
        $this->drawRequest->sowApproved = $drawRequestData['sowApproved'] ?? 0;
        $this->drawRequest->isDrawRequest = $drawRequestData['isDrawRequest'] ?? 0;
        $this->setProperties($this->drawRequest);
    }

    /**
     * Delete the draw request from DB
     *
     * @return void
     */
    public function delete(): void {
        if ($this->drawRequest instanceof tblDrawRequests) {
            $this->drawRequest->delete();
        }
    }

    /**
     * Loads categories associated with this draw request from the database.
     * @return void
     */
    public function loadCategories(): void {
        if (!$this->id) {
            return;
        }

        $categoriesData = tblDrawRequestCategories::GetAll(
            [tblDrawRequestCategories_db::COLUMN_DRAWID => $this->id],
            [tblDrawRequestCategories_db::COLUMN_ORDER => 'ASC']
        );

        $this->categories = [];
        foreach ($categoriesData as $categoryData) {
            $this->addCategory(new BorrowerDrawCategory($categoryData));
        }
        $this->sortCategories();
    }

    /**
     * Adds a BorrowerDrawCategory object to the draw request's categories.
     * @param BorrowerDrawCategory $category The category object to add.
     * @return void
     */
    private function addCategory(BorrowerDrawCategory $category): void {
        $this->categories[$category->id] = $category;
    }

    /**
     * Sorts the categories by their order property.
     * @return void
     */
    private function sortCategories(): void {
        uasort($this->categories, function($a, $b) {
            return $a->order <=> $b->order;
        });
    }

    /**
     * Retrieves all categories associated with this draw request.
     * @return array An array of BorrowerDrawCategory objects.
     */
    public function getAllCategories(): array {
        if (is_null($this->categories)) {
            $this->loadCategories();
        }
        return $this->categories ?? [];
    }

    /**
     * Retrieves all line items associated with this draw request.
     * @return array An array of BorrowerDrawLineItem objects.
     */
    public function getAllLineItems(): array {
        $lineItems = [];
        foreach ($this->getAllCategories() as $category) {
            $lineItems = array_merge($lineItems, $category->getAllLineItems());
        }
        return $lineItems;
    }

    /**
     * Update the draw request status based on line item completion.
     * If all line items are completed, set the status to 'approved'.
     * Otherwise, set the status to 'pending'.
     * @return bool True on success, False on failure.
     */

    public function updateDrawRequestStatus(?string $status, ?int $sowApproved = null, ?int $isDrawRequest = null): bool
    {
        if (!$this->drawRequest) {
            return false;
        }

        $requestAllLineItems = $this->getAllLineItems();

        $this->drawRequest->status = $status ?? self::STATUS_NEW;
        $this->drawRequest->sowApproved = $sowApproved ?? $this->drawRequest->sowApproved;
        $this->drawRequest->isDrawRequest = $isDrawRequest ?? $this->drawRequest->isDrawRequest;
        if (empty($requestAllLineItems) && $this->drawRequest->status !== self::STATUS_NEW) return false;

        $this->drawRequest->save();
        return true;
    }

    /**
     * Retrieves a specific category by its ID.
     * @param int $categoryId The ID of the category to retrieve.
     * @return BorrowerDrawCategory|null The BorrowerDrawCategory object if found, otherwise null.
     */
    public function getCategoryById($categoryId): ?BorrowerDrawCategory {
        if (is_null($this->categories)) {
            $this->loadCategories();
        }
        return $this->categories[$categoryId] ?? null;
    }

    /**
     * Delete categories by their IDs.
     * @param array $categoryIds Array of category IDs to delete.
     * @return void
     */
    public function deleteCategories(array $categoryIds): void {
        foreach ($categoryIds as $categoryId) {
            $category = $this->getCategoryById($categoryId);
            if ($category) {
                $category->delete();
            }
        }
    }

    /**
     * Delete line items by their IDs.
     * @param array $lineItemIds Array of line item IDs to delete.
     * @return void
     */
    public function deleteLineItems(array $lineItemIds): void {
        foreach ($lineItemIds as $lineItemId) {
            $lineItem = new BorrowerDrawLineItem();
            $lineItem->getDbObject()->id = $lineItemId;
            $lineItem->delete();
        }
    }
}
