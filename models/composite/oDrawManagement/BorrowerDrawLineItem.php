<?php
namespace models\composite\oDrawManagement;

use models\types\strongType;
use models\composite\oDrawManagement\traits\PropertiesMapper;
use models\lendingwise\tblDrawRequestLineItems;

class BorrowerDrawLineItem extends strongType
{
    use PropertiesMapper;

    /**
     * @var int|null The ID of the line item.
     */
    public ?int $id = null;

    /**
     * @var int|null The ID of the draw request this line item belongs to.
     */
    public ?int $drawId = null;

    /**
     * @var int|null The ID of the category this line item belongs to.
     */
    public ?int $categoryId = null;

    /**
     * @var string|null The name of the line item.
     */
    public ?string $name = null;

    /**
     * @var string|null The description of the line item.
     */
    public ?string $description = null;

    /**
     * @var int The display order of the line item.
     */
    public ?int $order = 1;

    /**
     * @var float The cost of the line item.
     */
    public ?float $cost = 0.00;

    /**
     * @var float The completed amount of the line item.
     */
    public ?float $completedAmount = 0.00;

    /**
     * @var float The completed percentage of the line item.
     */
    public ?float $completedPercent = 0.00;

    /**
     * @var float The requested amount of the line item.
     */
    public ?float $requestedAmount = 0.00;

    /**
     * @var float The disbursed amount of the line item.
     */
    public ?float $disbursedAmount = 0.00;

    /**
     * @var string|null Notes for the line item.
     */
    public ?string $notes = null;

    /**
     * @var string|null Lender notes for the line item.
     */
    public ?string $lenderNotes = null;

    /**
     * @var string|null The reason for rejecting the line item.
     */
    public ?string $rejectReason = null;

    /**
     * @var string|null The creation date.
     */
    public ?string $createdAt = null;

    /**
     * @var string|null The last update date.
     */
    public ?string $updatedAt = null;

    /**
     * @var tblDrawRequestLineItems|null The database table object for the line item.
     */
    public ?tblDrawRequestLineItems $lineItem = null;

    /**
     * BorrowerDrawLineItem constructor.
     * @param tblDrawRequestLineItems|null $lineItem The database line item object to initialize from.
     */
    public function __construct(?tblDrawRequestLineItems $lineItem = null) {
        if ($lineItem == null) $lineItem = new tblDrawRequestLineItems();
        $this->setProperties($lineItem);
    }

    /**
     * Saves the current line item object to the database.
     * @return array The result of the save operation.
     */
    public function save(array $lineItemData): array {
        $this->setFromArray($lineItemData);
        $saved = $this->lineItem->save();
        $this->id = $this->lineItem->id;
        return $saved;
    }

    /**
     * Sets the properties of the line item from an associative array.
     * @param array $lineItemData Associative array containing line item data.
     * @return void
     */
    private function setFromArray(array $lineItemData): void {
        $this->lineItem->id = $lineItemData['id'] ?? null;
        $this->lineItem->drawId = $lineItemData['drawId'];
        $this->lineItem->categoryId = $lineItemData['categoryId'];
        $this->lineItem->name = $lineItemData['name'];
        $this->lineItem->description = $lineItemData['description'] ?? $this->lineItem->description;
        $this->lineItem->order = $lineItemData['order'] ?? $this->lineItem->order;
        $this->lineItem->cost = $lineItemData['cost'] ?? $this->lineItem->cost;
        $this->lineItem->completedAmount = $lineItemData['completedAmount'] ?? $this->lineItem->completedAmount;
        $this->lineItem->completedPercent = $lineItemData['completedPercent'] ?? $this->lineItem->completedPercent;
        $this->lineItem->requestedAmount = $lineItemData['requestedAmount'] ?? $this->lineItem->requestedAmount;
        $this->lineItem->disbursedAmount = $lineItemData['disbursedAmount'] ?? $this->lineItem->disbursedAmount;
        $this->lineItem->notes = $lineItemData['notes'] ?? $this->lineItem->notes;
        $this->lineItem->lenderNotes = $lineItemData['lenderNotes'] ?? $this->lineItem->lenderNotes;
        $this->lineItem->rejectReason = $lineItemData['rejectReason'] ?? $this->lineItem->rejectReason;
        $this->setProperties($this->lineItem);
    }

    public function getDbObject(): tblDrawRequestLineItems {
        return $this->lineItem;
    }

    /**
     * Delete the lineitem from DB
     *
     * @return void
     */
    public function delete(): void {
        if ($this->lineItem instanceof tblDrawRequestLineItems) {
            $this->lineItem->delete();
        }
    }
}
