<?php
namespace models\composite\oDrawManagement\traits;

/**
 * Trait PropertiesMapper
 *
 * Provides automated property mapping from database objects to class properties
 * and a consistent way to convert composite objects to arrays.
 */
trait PropertiesMapper
{
    /**
     * Generic setProperties method that can be used by most composite models.
     * It automatically maps public properties from a source object to the current object.
     *
     * @param object $dbObject The database object to map from.
     */
    protected function setProperties(object $dbObject): void
    {
        // Get the expected property name for the dbObject itself (e.g., 'drawRequest', 'category')
        $dbObjectProperty = $this->getImplementingClassPropertyName();
        if (property_exists($this, $dbObjectProperty)) {
            $this->{$dbObjectProperty} = $dbObject;
        }

        // Get public properties of the source DB object
        $sourceProps = (new \ReflectionClass($dbObject))->getProperties(\ReflectionProperty::IS_PUBLIC);

        foreach ($sourceProps as $sourceProp) {
            $propName = $sourceProp->getName();
            // If the current class has a public property with the same name, set it.
            if (property_exists($this, $propName)) {
                $this->{$propName} = $sourceProp->getValue($dbObject);
            }
        }
    }

    /**
     * Converts the composite object to an array, based on its public properties.
     * This avoids manual and duplicated toArray() implementations.
     *
     * @return array
     */
    public function toArray(): array
    {
        $data = [];
        $reflection = new \ReflectionClass($this);
        $properties = $reflection->getProperties(\ReflectionProperty::IS_PUBLIC);

        foreach ($properties as $property) {
            $propName = $property->getName();
            $propValue = $property->getValue($this);

            // Recursively convert child objects/arrays to arrays
            if (is_array($propValue)) {
                $data[$propName] = array_map(function ($item) {
                    return is_object($item) && method_exists($item, 'toArray') ? $item->toArray() : $item;
                }, $propValue);
            } elseif (is_object($propValue) && method_exists($propValue, 'toArray')) {
                $data[$propName] = $propValue->toArray();
            } else {
                // Exclude the raw DB object from the final array to avoid duplication
                $dbObjectProperty = $this->getImplementingClassPropertyName();
                if ($propName !== $dbObjectProperty) {
                    $data[$propName] = $propValue;
                }
            }
        }
        return $data;
    }

    /**
     * Helper to determine the database object property name based on class naming conventions.
     *
     * @return string The expected database object property name.
     */
    private function getImplementingClassPropertyName(): string
    {
        $className = (new \ReflectionClass($this))->getShortName();
        // Map composite class names to their expected database object property names
        $classToPropertyMap = [
            'DrawRequest' => 'drawRequest',
            'BorrowerDrawCategory' => 'category',
            'BorrowerDrawLineItem' => 'lineItem',
            'DrawRequestsHistory' => 'drawRequestHistory',
            'SowCategory' => 'category',
            'SowLineItem' => 'lineItem',
            'SowTemplate' => 'template',
        ];

        return $classToPropertyMap[$className] ?? lcfirst($className);
    }
}
