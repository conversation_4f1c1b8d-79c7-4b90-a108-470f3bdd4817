<?php

namespace pages\backoffice\api_v2\draw_management\ExportPdf;

use models\portals\BackofficePage;
use models\standard\HTTP;
use models\pdf\CustomTCPDF;
use models\cypher;
use models\composite\oDrawManagement\DrawRequestManager;
use models\composite\oDrawManagement\DrawRequest;

/**
 * Class ExportPdf
 *
 * API endpoint for exporting draw management table as PDF
 *
 * @package pages\backoffice\api_v2\draw_management
 */
class ExportPdf extends BackofficePage
{
    use PdfTableConfigurationTrait;
    /**
     * Handle POST requests to export table HTML as PDF
     *
     * @return void
     */
    public static function Post(): void
    {
        parent::Init();

        $postData = file_get_contents("php://input");
        $postData = json_decode($postData, true);

        $lmrId = $postData['lmrId'] ?? '';
        $exportType = $postData['exportType'] ?? 'main';
        $historyId = $postData['historyId'] ?? null;
        $modalContent = $postData['modalContent'] ?? null;

        if (empty($lmrId)) {
            HTTP::ExitJSON(["success" => false, "message" => "LMR ID is required."]);
        }

        if ($lmrId && !is_numeric($lmrId)) {
            $lmrId = cypher::myDecryption($lmrId);
        }
        $lmrId = (int)$lmrId;

        try {
            if ($exportType === 'history') {
                if (empty($historyId) || empty($modalContent)) {
                    HTTP::ExitJSON(["success" => false, "message" => "History ID and modal content are required for history export."]);
                }
                $pdfContent = self::generateHistoryPdfFromData($lmrId, $historyId, $modalContent);
                $filename = self::generateHistoryFilename($lmrId, $historyId);
            } else {
                $pdfContent = self::generatePdfFromData($lmrId);
                $filename = self::generateFilename($lmrId);
            }

            if (!$pdfContent) {
                HTTP::ExitJSON(["success" => false, "message" => "Failed to generate PDF."]);
            }

            // Encode PDF content as base64 for AJAX response
            $pdfBase64 = base64_encode($pdfContent);

            HTTP::ExitJSON([
                "success" => true,
                "filename" => $filename,
                "pdf_data" => $pdfBase64,
                "message" => "PDF generated successfully"
            ]);

        } catch (\Exception $e) {
            HTTP::ExitJSON(["success" => false, "message" => "An error occurred: " . $e->getMessage()]);
        }
    }

    /**
     * Generate PDF from draw management data using TCPDF
     *
     * @param int $lmrId
     * @return string|false
     */
    private static function generatePdfFromData(int $lmrId)
    {
        try {
            // Load draw request data
            $drawRequestManager = DrawRequestManager::forLoanFile($lmrId);
            $requestData = $drawRequestManager->getDrawRequestDataArray();
            $categoriesData = [];

            if (isset($requestData['status']) && $requestData['status'] !== DrawRequest::STATUS_NEW) {
                $categoriesData = $requestData['categories'];
            }

            if (empty($categoriesData)) {
                throw new \Exception("No draw management data found for loan file ID: " . $lmrId);
            }

            // Initialize TCPDF with landscape orientation for better table layout
            $pdf = new CustomTCPDF('L', 'mm', 'A4', true, 'UTF-8', false);

            // Set document information
            $pdf->SetCreator('LendingWise');
            $pdf->SetAuthor('LendingWise');
            $pdf->SetTitle('Draw Request Report');
            $pdf->SetSubject('Draw Request Export - LMR ' . $lmrId);

            // Set default header data
            $pdf->SetHeaderData('', 0, 'Draw Request Report', 'Loan File ID: ' . $lmrId . ' | Generated: ' . date('M j, Y g:i A'));

            // Set header and footer fonts
            $pdf->setHeaderFont(['helvetica', 'B', 12]);
            $pdf->setFooterFont(['helvetica', '', 8]);

            // Set default monospaced font
            $pdf->SetDefaultMonospacedFont('courier');

            // Set margins (landscape orientation)
            $pdf->SetMargins(15, 30, 15);
            $pdf->SetHeaderMargin(5);
            $pdf->SetFooterMargin(10);

            // Set auto page breaks
            $pdf->SetAutoPageBreak(true, 20);

            // Set image scale factor
            $pdf->setImageScale(1.25);

            // Add a page
            $pdf->AddPage();

            // Generate the PDF content
            self::addReportHeader($pdf, $lmrId, $requestData);
            self::addDataTable($pdf, $categoriesData, $requestData);

            // Return PDF content as string
            return $pdf->Output('', 'S');

        } catch (\Exception $e) {
            error_log("PDF Generation Error: " . $e->getMessage());
            return false;
        }
    }

    /**
     * Add report header with loan information
     *
     * @param CustomTCPDF $pdf
     * @param int $lmrId
     * @param array $requestData
     * @return void
     */
    private static function addReportHeader(CustomTCPDF $pdf, int $lmrId, array $requestData): void
    {
        // Set font for header
        $pdf->SetFont('helvetica', 'B', 14);
        $pdf->Cell(0, 10, 'Draw Request Report', 0, 1, 'C');
        $pdf->Ln(3);

        // Add loan file information
        $pdf->SetFont('helvetica', '', 11);
        $pdf->Cell(60, 8, 'Loan File ID:', 0, 0, 'L');
        $pdf->SetFont('helvetica', 'B', 11);
        $pdf->Cell(0, 8, $lmrId, 0, 1, 'L');

        $pdf->SetFont('helvetica', '', 11);
        $pdf->Cell(60, 8, 'Report Generated:', 0, 0, 'L');
        $pdf->SetFont('helvetica', 'B', 11);
        $pdf->Cell(0, 8, date('F j, Y g:i A'), 0, 1, 'L');

        // Add status information
        if (isset($requestData['status'])) {
            $statusDisplay = self::getStatusDisplay($requestData);
            $pdf->SetFont('helvetica', '', 11);
            $pdf->Cell(60, 8, 'Status:', 0, 0, 'L');
            $pdf->SetFont('helvetica', 'B', 11);
            $pdf->Cell(0, 8, $statusDisplay, 0, 1, 'L');
        }

        // Add report type
        $isDrawRequest = isset($requestData['sowApproved']) && $requestData['sowApproved'];
        $pdf->SetFont('helvetica', '', 11);
        $pdf->Cell(60, 8, 'Report Type:', 0, 0, 'L');
        $pdf->SetFont('helvetica', 'B', 11);
        $pdf->Cell(0, 8, $isDrawRequest ? 'Draw Request' : 'Scope of Work', 0, 1, 'L');

        $pdf->Ln(8);
    }

    /**
     * Get status display text
     *
     * @param array $requestData
     * @return string
     */
    private static function getStatusDisplay(array $requestData): string
    {
        switch ($requestData['status']) {
            case DrawRequest::STATUS_PENDING:
                return 'Submitted';
            case DrawRequest::STATUS_APPROVED:
                return isset($requestData['isDrawRequest']) && $requestData['isDrawRequest'] ? 'Draw Request Approved' : 'Draw 1 Pending';
            case DrawRequest::STATUS_REJECTED:
                return 'Rejected';
            default:
                return ucfirst($requestData['status']);
        }
    }

    /**
     * Add data table to PDF
     *
     * @param CustomTCPDF $pdf
     * @param array $categoriesData
     * @param array $requestData
     * @return void
     */
    private static function addDataTable(CustomTCPDF $pdf, array $categoriesData, array $requestData): void
    {
        $isDrawRequest = isset($requestData['sowApproved']) && $requestData['sowApproved'];
        $tableConfig = self::getTableConfiguration($isDrawRequest ? 'draw_request' : 'scope_of_work');
        self::generateTable($pdf, $categoriesData, $tableConfig);
    }

    /**
     * Generate table with given configuration
     *
     * @param CustomTCPDF $pdf
     * @param array $categoriesData
     * @param array $config
     * @return void
     */
    private static function generateTable(CustomTCPDF $pdf, array $categoriesData, array $config): void
    {
        // Draw headers
        self::drawTableHeaders($pdf, $config);

        // Draw data rows
        $totals = self::drawTableData($pdf, $categoriesData, $config);

        // Draw totals if configured
        if (!empty($config['show_totals'])) {
            self::drawTableTotals($pdf, $config, $totals);
        }
    }

    /**
     * Draw table headers
     *
     * @param CustomTCPDF $pdf
     * @param array $config
     * @return void
     */
    private static function drawTableHeaders(CustomTCPDF $pdf, array $config): void
    {
        $pdf->SetFont('helvetica', 'B', $config['use_multicell_headers'] ? 9 : 10);
        $pdf->SetFillColor(240, 240, 240);

        if ($config['use_multicell_headers']) {
            // Use MultiCell for headers that might wrap
            $yPos = $pdf->GetY();
            $xPos = $pdf->GetX();

            foreach ($config['headers'] as $header => $headerConfig) {
                $pdf->MultiCell(
                    $headerConfig['width'],
                    $config['header_height'],
                    $header,
                    1,
                    'C',
                    true,
                    0,
                    $xPos,
                    $yPos,
                    true,
                    0,
                    false,
                    true,
                    $config['header_height'],
                    'M'
                );
                $xPos += $headerConfig['width'];
            }
            $pdf->Ln($config['header_height']);
        } else {
            // Use regular Cell for simple headers
            foreach ($config['headers'] as $header => $headerConfig) {
                $pdf->Cell($headerConfig['width'], $config['header_height'], $header, 1, 0, 'C', true);
            }
            $pdf->Ln();
        }
    }

    /**
     * Draw table data rows
     *
     * @param CustomTCPDF $pdf
     * @param array $categoriesData
     * @param array $config
     * @return array
     */
    private static function drawTableData(CustomTCPDF $pdf, array $categoriesData, array $config): array
    {
        $pdf->SetFont('helvetica', '', 9);
        $totals = [];

        foreach ($categoriesData as $category) {
            if (!empty($category['lineItems'])) {
                // Category header
                $pdf->SetFont('helvetica', 'B', 10);
                $pdf->SetFillColor(225, 240, 255);
                $totalWidth = array_sum(array_column($config['headers'], 'width'));
                $pdf->Cell($totalWidth, 8, strtoupper($category['name']), 1, 1, 'C', true);

                // Line items
                $pdf->SetFont('helvetica', '', 9);
                $pdf->SetFillColor(255, 255, 255);

                foreach ($category['lineItems'] as $lineItem) {
                    foreach ($config['headers'] as $header => $headerConfig) {
                        $value = self::formatCellValue($lineItem, $config['data_fields'][$header], $headerConfig);
                        $pdf->Cell($headerConfig['width'], 8, $value, 1, 0, $headerConfig['align']);

                        // Track totals for numeric fields
                        if (isset($headerConfig['format']) && in_array($headerConfig['format'], ['currency']) && isset($config['data_fields'][$header])) {
                            $field = $config['data_fields'][$header];
                            if (is_string($field) && isset($lineItem[$field])) {
                                $totals[$header] = ($totals[$header] ?? 0) + $lineItem[$field];
                            }
                        }
                    }
                    $pdf->Ln();
                }
            }
        }

        return $totals;
    }

    /**
     * Draw table totals row
     *
     * @param CustomTCPDF $pdf
     * @param array $config
     * @param array $totals
     * @return void
     */
    private static function drawTableTotals(CustomTCPDF $pdf, array $config, array $totals): void
    {
        $pdf->Ln(3);
        $pdf->SetFont('helvetica', 'B', 10);
        $pdf->SetFillColor(240, 240, 240);

        $isFirst = true;
        foreach ($config['headers'] as $header => $headerConfig) {
            if ($isFirst) {
                $value = 'TOTALS';
                $isFirst = false;
            } elseif (isset($totals[$header])) {
                $value = '$' . number_format($totals[$header], 2);
            } elseif ($header === '% Completed' && isset($totals['Total Budget']) && isset($totals['Completed Renovations'])) {
                $totalPercent = $totals['Total Budget'] > 0 ? round($totals['Completed Renovations'] / $totals['Total Budget'] * 100) : 0;
                $value = $totalPercent . '%';
            } else {
                $value = '';
            }

            $pdf->Cell($headerConfig['width'], 10, $value, 1, 0, $headerConfig['align'], true);
        }
        $pdf->Ln();
    }

    /**
     * Format cell value based on configuration
     *
     * @param array $lineItem
     * @param mixed $field
     * @param array $config
     * @return string
     */
    private static function formatCellValue(array $lineItem, $field, array $config): string
    {
        $format = $config['format'] ?? 'text';

        switch ($format) {
            case 'currency':
                $value = is_string($field) ? ($lineItem[$field] ?? 0) : 0;
                return '$' . number_format($value, 2);

            case 'percentage':
                $value = is_string($field) ? ($lineItem[$field] ?? 0) : 0;
                return round($value) . '%';

            case 'percentage_with_currency':
                if (is_array($field) && count($field) === 2) {
                    $requestedAmount = $lineItem[$field[0]] ?? 0;
                    $cost = $lineItem[$field[1]] ?? 0;
                    $percent = $cost > 0 ? round($requestedAmount / $cost * 100) : 0;
                    return $percent . '% | $' . number_format($requestedAmount, 2);
                }
                return '';

            case 'truncate':
                $value = is_string($field) ? ($lineItem[$field] ?? '') : '';
                $length = $config['length'] ?? 50;
                return substr($value, 0, $length);

            default:
                return is_string($field) ? ($lineItem[$field] ?? '') : '';
        }
    }

    /**
     * Generate filename for the PDF export
     *
     * @param int $lmrId
     * @return string
     */
    private static function generateFilename(int $lmrId): string
    {
        $timestamp = date('Y-m-d_H-i-s');
        $lmrPart = $lmrId ? "_LMR{$lmrId}" : '';
        return "draw_request_report{$lmrPart}_{$timestamp}.pdf";
    }

    /**
     * Generate PDF from draw history modal content
     *
     * @param int $lmrId
     * @param int $historyId
     * @param string $modalContent
     * @return string|false
     */
    private static function generateHistoryPdfFromData(int $lmrId, int $historyId, string $modalContent)
    {
        try {
            // Load draw request manager to get history data
            $drawRequestManager = DrawRequestManager::forLoanFile($lmrId);
            $historyRecords = $drawRequestManager->getDrawRequestHistory();

            // Find the specific history record
            $historyRecord = null;
            foreach ($historyRecords as $record) {
                if ($record['id'] == $historyId) {
                    $historyRecord = $record;
                    break;
                }
            }

            if (!$historyRecord) {
                throw new \Exception("History record not found for ID: " . $historyId);
            }

            // Get line items history data
            $lineItemsHistoryData = $drawRequestManager->getDrawRequestLineItemsHistory($historyId);

            // Initialize TCPDF with landscape orientation for better table layout
            $pdf = new CustomTCPDF('L', 'mm', 'A4', true, 'UTF-8', false);

            // Set document information
            $pdf->SetCreator('LendingWise');
            $pdf->SetAuthor('LendingWise');
            $pdf->SetTitle('Draw Request History Report');
            $pdf->SetSubject('Draw Request History Export - LMR ' . $lmrId);

            // Set default header data
            $pdf->SetHeaderData('', 0, 'Draw Request History Report', 'Loan File ID: ' . $lmrId . ' | Generated: ' . date('M j, Y g:i A'));

            // Set header and footer fonts
            $pdf->setHeaderFont(['helvetica', 'B', 12]);
            $pdf->setFooterFont(['helvetica', '', 8]);

            // Set default monospaced font
            $pdf->SetDefaultMonospacedFont('courier');

            // Set margins (landscape orientation)
            $pdf->SetMargins(15, 30, 15);
            $pdf->SetHeaderMargin(5);
            $pdf->SetFooterMargin(10);

            // Set auto page breaks
            $pdf->SetAutoPageBreak(true, 20);

            // Set image scale factor
            $pdf->setImageScale(1.25);

            // Add a page
            $pdf->AddPage();

            // Generate the PDF content
            self::addHistoryReportHeader($pdf, $lmrId, $historyRecord);
            self::addHistoryDataTable($pdf, $lineItemsHistoryData);

            // Return PDF content as string
            return $pdf->Output('', 'S');

        } catch (\Exception $e) {
            error_log("History PDF Generation Error: " . $e->getMessage());
            return false;
        }
    }

    /**
     * Add history report header to PDF
     *
     * @param CustomTCPDF $pdf
     * @param int $lmrId
     * @param object $historyRecord
     * @return void
     */
    private static function addHistoryReportHeader(CustomTCPDF $pdf, int $lmrId, $historyRecord): void
    {
        // Set font for header
        $pdf->SetFont('helvetica', 'B', 14);
        $pdf->Cell(0, 10, 'Draw Request History', 0, 1, 'C');
        $pdf->Ln(5);

        // Set font for details
        $pdf->SetFont('helvetica', '', 10);

        // Draw information section
        $pdf->SetFont('helvetica', 'B', 12);
        $pdf->Cell(0, 8, 'Draw Information', 0, 1, 'L');
        $pdf->SetFont('helvetica', '', 10);

        $statusText = ucfirst($historyRecord['status']);
        switch ($historyRecord['status']) {
            case 'pending':
                $statusText = 'Submitted';
                break;
            case 'approved':
                $statusText = 'Approved';
                break;
            case 'rejected':
                $statusText = 'Rejected';
                break;
        }

        $pdf->Cell(60, 6, 'Status:', 0, 0, 'L');
        $pdf->Cell(0, 6, $statusText, 0, 1, 'L');

        $submissionDate = $historyRecord['submittedAt'] ? date('M j, Y g:i A', strtotime($historyRecord['submittedAt'])) : '-';
        $pdf->Cell(60, 6, 'Submission Date:', 0, 0, 'L');
        $pdf->Cell(0, 6, $submissionDate, 0, 1, 'L');

        $pdf->Cell(60, 6, 'Amount Requested:', 0, 0, 'L');
        $pdf->Cell(0, 6, '$' . number_format($historyRecord['requestedAmount'], 2), 0, 1, 'L');

        if ($historyRecord['status'] === 'approved' && $historyRecord['approvedAmount'] > 0) {
            $pdf->Cell(60, 6, 'Amount Approved:', 0, 0, 'L');
            $pdf->Cell(0, 6, '$' . number_format($historyRecord['approvedAmount'], 2), 0, 1, 'L');
        }

        $pdf->Ln(10);
    }

    /**
     * Add history data table to PDF
     *
     * @param CustomTCPDF $pdf
     * @param array $lineItemsHistoryData
     * @return void
     */
    private static function addHistoryDataTable(CustomTCPDF $pdf, array $lineItemsHistoryData): void
    {
        if (empty($lineItemsHistoryData)) {
            $pdf->SetFont('helvetica', '', 10);
            $pdf->Cell(0, 10, 'No line items data available for this draw request history.', 0, 1, 'C');
            return;
        }

        $tableConfig = self::getTableConfiguration('history');
        self::generateTable($pdf, $lineItemsHistoryData, $tableConfig);
    }

    /**
     * Generate filename for the history PDF export
     *
     * @param int $lmrId
     * @param int $historyId
     * @return string
     */
    private static function generateHistoryFilename(int $lmrId, int $historyId): string
    {
        $timestamp = date('Y-m-d_H-i-s');
        $lmrPart = $lmrId ? "_LMR{$lmrId}" : '';
        $historyPart = $historyId ? "_History{$historyId}" : '';
        return "draw_history_report{$lmrPart}{$historyPart}_{$timestamp}.pdf";
    }
}
