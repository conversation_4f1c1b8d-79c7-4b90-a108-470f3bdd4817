<?php
use models\portals\PublicPage;
use models\JSCompiler;
use models\standard\Strings;
// No longer need SowTemplateManager here, as DrawRequestManager handles it.
use models\composite\oDrawManagement\DrawRequestManager;
use models\composite\oDrawManagement\DrawRequest;
use models\cypher;
use models\Request;

session_start();
require '../includes/util.php';

// --- Variable Setup ---
$userType = 'borrower';
$LMRId = Request::GetClean('lmrid');
if (!is_numeric($LMRId)) {
    $LMRId = (int)cypher::myDecryption($LMRId);
}

// --- Data Fetching ---
$drawRequestManager = DrawRequestManager::forLoanFile($LMRId);
$drawRequest = $drawRequestManager->getDrawRequest();

// This will be the single, complete state object for our JavaScript.
// It's only needed if we are showing the editable SOW form.
$initialDrawDataForJs = [];
if ($drawRequest && !$drawRequest->sowApproved) {
    // This single call now provides the complete state: draw data + permissions.
    $initialDrawDataForJs = $drawRequestManager->getDrawRequestDataArray();
}

// --- Logic for Displaying Server-Side Status Alerts ---
$displayStatus = '';
$displayStatusClass = '';
if ($drawRequest) {
    switch ($drawRequest->status) {
        case DrawRequest::STATUS_PENDING:
            $message = $drawRequest->isDrawRequest ? 'Your draw request is awaiting approval.' : 'Your Scope of Work is awaiting approval.';
            $displayStatus = $message;
            $displayStatusClass = 'alert-warning';
            break;
        case DrawRequest::STATUS_APPROVED:
            $displayStatus = $drawRequest->isDrawRequest ? 'Your draw request has been approved.' : 'Your Scope of Work has been approved.';
            $displayStatusClass = 'alert-success';
            break;
        case DrawRequest::STATUS_REJECTED:
            $displayStatus = 'Please see revision requests under lender notes, then revise and resubmit.';
            $displayStatusClass = 'alert-danger';
            break;
    }
}
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1" />
    <link href="/assets/images/favicon-whitelabel.png" rel="SHORTCUT ICON" />
    <title>Submit/Revise Scope of Work</title>
    <?php
    PublicPage::Init();
    echo JSCompiler::scripts();
    echo JSCompiler::stylesheets();

    // Conditionally include JS/CSS only for the interactive SOW editor view.
    if ($drawRequest && !$drawRequest->sowApproved) {
        Strings::includeMyScript(['/assets/js/drawManagement/common.js']);
        Strings::includeMyScript(['/assets/js/drawManagement/borrower.js']);
        Strings::includeMyScript(['/assets/js/3rdParty/sortable/Sortable.min.js']);
        Strings::includeMyCSS(['/assets/css/components/drawManagement.css']);
    }
    ?>
</head>
<body translate="no">

<style>
    /* All your existing CSS styles remain unchanged here */
    html { font-size: 12px; }
    body { background-color: #f8f9fa; font-size: 1.2rem; }
    /* ... etc ... */
</style>

    <div class="container mt-4 mb-5">
        <!-- Main Content Card -->
        <div class="card p-4 p-md-5 shadow-sm">
            <?php
            // This logic correctly determines which view to show.
            if ($drawRequest && $drawRequest->sowApproved) {
                // View for an active draw request (less interactive).
                require 'drawRequest.php';
            } else {
                // View for the initial Scope of Work editor.
                require '../backoffice/drawManagement/partials/_draw-management-card.php';
            }
            ?>
            <input type="hidden" id="lmrid" value="<?php echo htmlspecialchars($_REQUEST['lmrid'] ?? ''); ?>">
        </div>
        <?php
            // The history partial is common to both views.
            if ($drawRequest) { // Only show history if a draw request exists.
                require('../backoffice/drawManagement/loanFile/drawHistory.php');
            }
        ?>
    </div>

    <?php
    // This script block is ONLY included when we need the interactive SOW editor.
    if ($drawRequest && !$drawRequest->sowApproved):
    ?>
    <script>
        // Pass the single, complete state object to JavaScript.
        const initialDrawData = <?= json_encode($initialDrawDataForJs, JSON_HEX_TAG | JSON_HEX_AMP); ?>;
        console.log(initialDrawData);
        $(document).ready(function() {
            // Initialize the DrawManagement module with the user type and the complete state object.
            // No separate `settings` object is needed anymore.
            DrawManagement.init('<?=$userType ?>', initialDrawData);
        });
    </script>
    <?php endif; ?>

</body>
</html>
