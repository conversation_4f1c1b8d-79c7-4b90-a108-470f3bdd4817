/**
 * Lender-specific Draw Management Functionality
 *
 * This file manages the lender's interaction with the Scope of Work Template.
 * Like the borrower's script, it operates on the central `DrawManagement.state` object.
 * The lender's primary role is defining the template categories, line items, and settings.
 */
DrawManagement.lender = {
    /**
     * Initialize lender-specific configuration.
     */
    init: function() {
        DrawManagement.config.pcid = $('#pcid').val();
        DrawManagement.config.dataKey = 'pcid';
        DrawManagement.config.dataUrl = `/backoffice/api_v2/draw_management/lender/SowCategories?pcid=${DrawManagement.config.pcid}`;
        DrawManagement.config.saveLineItemsSuccessMessage = 'Line items saved successfully!';
        // Most event handlers are in common.js, this is for any lender-specific ones.
        this.initEventHandlers();
    },

    /**
     * Initializes lender-specific event handlers. (Currently, most are common)
     */
    initEventHandlers: function() {
        // Placeholder for any future lender-only event bindings.
        // For instance, if the settings card had more complex interactions.
    },

    /**
     * Populates the template settings form from the initial state loaded from the server.
     * This is a "state -> view" operation.
     */
    initTemplateSettings: function() {
        const settings = DrawManagement.state; // The root of the state holds the settings data.

        // Helper function to set a switch and its hidden input
        const setSwitch = (key, value) => {
            $(`#${key}Tog`).prop('checked', value === 1);
            $(`#${key}`).val(value);
        };

        setSwitch('allowBorrowersAddEditCategories', settings.allowBorrowersAddEditCategories);
        setSwitch('allowBorrowersDeleteCategories', settings.allowBorrowersDeleteCategories);
        setSwitch('allowBorrowersAddEditLineItems', settings.allowBorrowersAddEditLineItems);
        setSwitch('allowBorrowersDeleteLineItems', settings.allowBorrowersDeleteLineItems);
        setSwitch('allowBorrowersSOWRevisions', settings.allowBorrowersSOWRevisions);
        setSwitch('allowBorrowersExceedFinancedRehabCostOnRevision', settings.allowBorrowersExceedFinancedRehabCostOnRevision);

        $('#drawFee').val(parseFloat(settings.drawFee || 0).toFixed(2));
    },

    /**
     * Builds the JSON payload for saving line items by reading directly from the state object.
     * This avoids fragile DOM scraping and ensures data integrity.
     * @returns {object} - The payload for the API request.
     */
    buildLineItemsJson: function() {
        const groupedLineItems = {};

        // Iterate over the categories in our state object.
        DrawManagement.state.categories.forEach(category => {
            // Map the line items in the state to the format expected by the backend.
            const itemsForThisCategory = category.lineItems.map((item, index) => {
                // Handle newly added items that have a temporary string ID.
                const id = (typeof item.id === 'string' && item.id.startsWith('new_')) ? null : item.id;
                return {
                    id: id,
                    categoryId: category.id,
                    name: item.name,
                    description: item.description || '',
                    order: index + 1 // Re-calculate order based on current position.
                };
            });
            groupedLineItems[category.id] = itemsForThisCategory;
        });

        return {
            pcid: DrawManagement.config.pcid,
            lineItems: groupedLineItems
        };
    },

    /**
     * Renders the UI for a list of line items within a specific category for the lender view.
     * @param {Array} lineItems - An array of line item objects from the state.
     * @param {jQuery} $tbodyElement - The jQuery object for the table body to append rows to.
     */
    renderLineItemRowsUI: function(lineItems, $tbodyElement) {
        const rowTemplate = document.getElementById('line-item-row-template');
        if (!rowTemplate) {
            console.error("line-item-row-template not found!");
            return;
        }

        if (lineItems && lineItems.length > 0) {
            lineItems.forEach(item => {
                const clone = rowTemplate.content.cloneNode(true);
                const $row = $(clone).find('tr');

                // Populate the row from the item object.
                $row.attr('data-line-item-id', item.id);

                $row.find('.line-item-name-display').text(item.name);
                $row.find('.line-item-name-input').val(item.name);

                $row.find('.line-item-description-display').text(item.description);
                $row.find('.line-item-description-input').val(item.description);

                $tbodyElement.append($row);
            });
        }
    }
};
