/**
 * Common Draw Management Functionality
 *
 * This script provides the core logic for the draw management interface, shared between
 * lender and borrower views. It follows a state-driven architecture where a central `state` object
 * is the single source of truth. The UI is a direct reflection of this state.
 *
 * Data Flow:
 * 1. Initial State -> Render UI
 * 2. User Interaction -> Update State -> Re-render affected UI part
 * 3. Save Action -> Send State to API -> API returns new State -> Update State -> Re-render UI
 */

window.DrawManagement = window.DrawManagement || {};

DrawManagement = {
    // --- Configuration ---
    config: {
        categoriesModified: false,
        lineItemsModified: false,
        pcid: null,
        lmrid: null,
        maxCategories: 20,
        userType: null, // 'lender' or 'borrower'
        dataKey: null,
        dataUrl: null,
        isDraft: false
    },

    // --- State Management ---
    state: {}, // The single source of truth for all draw data.
    lastSavedState: {}, // A deep copy of the state after the last successful save/fetch.

    // --- UI Element Cache ---
    elements: {},

    /**
     * Initializes the entire draw management module.
     * @param {string} userType - 'lender' or 'borrower'.
     * @param {object} initialData - The initial state data passed from the server.
     */
    init: function(userType, initialData = {}) {
        this.config.userType = userType;
        this.config.maxCategories = parseInt($('#maxCategories').val()) || 20;

        this.elements = {
            $categoriesContainer: $('.categories-container'),
            $lineItemCategoriesContainer: $('#lineItemCategoriesContainer'),
            $saveCatBtn: $('.save-cat'),
            $saveLineItemsBtn: $('.save-line-items'),
            $saveCategoryModalBtn: $('#saveCategoryModalBtn'),
            $openAddCategoryModalBtn: $('#openAddCategoryModalBtn')
        };

        // Initialize user-specific module (lender.js or borrower.js)
        this[this.config.userType]?.init();

        // Set the initial state and create a backup for reverting changes.
        this.state = initialData;
        this.lastSavedState = JSON.parse(JSON.stringify(initialData)); // Deep copy

        // Initial render and setup.
        this.renderAll();
        this.initCommonEventHandlers();
        this.initSortable();
        this.initStepper();

        // Apply user-specific permissions after rendering.
        this[this.config.userType]?.applyPermissions?.();
    },

    /**
     * Central rendering function to update the entire UI from the current state.
     */
    renderAll: function() {
        this.renderCategoriesUI();
        this.renderLineItemsForCategoriesUI();
        this.checkAddMoreCategories();

        if (this.config.userType === 'lender') {
            this.lender.initTemplateSettings();
        }

        // Disable save buttons as there are no modifications yet.
        this.elements.$saveCatBtn.prop('disabled', true);
        this.elements.$saveLineItemsBtn.prop('disabled', true);
        this.config.categoriesModified = false;
        this.config.lineItemsModified = false;
    },

    // ========================================================================
    //                          DATA & API HANDLING
    // ========================================================================

    /**
     * Fetches the latest data from the server, updates the state, and re-renders the UI.
     * @returns {Promise} A jQuery AJAX promise.
     */
    fetchDataAndUpdateState: function() {
        const self = this;
        if (!self.config.dataUrl) {
            console.error("Data URL is not configured.");
            return $.Deferred().reject().promise();
        }

        return $.ajax({
            url: self.config.dataUrl,
            type: 'GET',
            dataType: 'json'
        }).done(function(response) {
            if (response.success && response.data) {
                self.state = response.data;
                self.lastSavedState = JSON.parse(JSON.stringify(response.data));
                self.renderAll();
                self[self.config.userType]?.applyPermissions?.();
            } else {
                toastrNotification(`Error loading data: ${response.message || 'Unknown error'}`, 'error');
            }
        }).fail(function(xhr) {
            const errorMsg = xhr.responseJSON?.message || 'A server error occurred.';
            toastrNotification(`Error loading data: ${errorMsg}`, 'error');
        });
    },

    /**
     * Saves categories to the server. On success, updates the state with the response.
     */
    saveCategories: function(btn) {
        const self = this;
        const payload = this.buildCategoriesJson();
        const $saveBtn = $(btn);
        const originalBtnText = $saveBtn.text();

        $saveBtn.prop('disabled', true).text('Saving...');

        $.ajax({
            url: `/backoffice/api_v2/draw_management/${self.config.userType}/SowCategories`,
            type: 'POST',
            data: JSON.stringify(payload),
            contentType: 'application/json',
            dataType: 'json',
            success: function(response) {
                if (response.success && response.data) {
                    toastrNotification('Categories saved successfully!', 'success');
                    self.state = response.data; // Update state with server response (which includes new IDs)
                    self.lastSavedState = JSON.parse(JSON.stringify(response.data));
                    self.renderAll();
                    if ($saveBtn.attr('id') === 'save-next') {
                         self.switchStep('line-items');
                    }
                } else {
                    toastrNotification(`Error saving categories: ${response.message || 'Unknown error'}`, 'error');
                    $saveBtn.prop('disabled', false); // Re-enable on failure
                }
            },
            error: function(xhr) {
                const errorMsg = xhr.responseJSON?.message || 'A server error occurred.';
                toastrNotification(`Error saving categories: ${errorMsg}`, 'error');
                $saveBtn.prop('disabled', false);
            },
            complete: function() {
                $saveBtn.text(originalBtnText);
            }
        });
    },

    /**
     * Saves line items to the server. On success, may trigger a page reload for borrowers.
     */
    saveLineItems: function() {
        const self = this;
        const payload = this[this.config.userType].buildLineItemsJson();

        self.elements.$saveLineItemsBtn.prop('disabled', true).text('Saving...');

        $.ajax({
            url: `/backoffice/api_v2/draw_management/${self.config.userType}/SowLineItems`,
            type: 'POST',
            data: JSON.stringify(payload),
            contentType: 'application/json',
            dataType: 'json',
            success: function(response) {
                if (response.success) {
                    toastrNotification(self.config.saveLineItemsSuccessMessage, 'success');
                    self.config.lineItemsModified = false;

                    if (self.config.userType === 'borrower') {
                        setTimeout(() => window.location.reload(), 1500);
                    } else {
                        // For lenders, refresh state and UI
                        self.state = response.data;
                        self.lastSavedState = JSON.parse(JSON.stringify(response.data));
                        self.renderAll();
                    }
                } else {
                    toastrNotification(`Error saving line items: ${response.message || 'Unknown error'}`, 'error');
                    self.elements.$saveLineItemsBtn.prop('disabled', false);
                }
            },
            error: function(xhr) {
                const errorMsg = xhr.responseJSON?.message || 'A server error occurred.';
                toastrNotification(`Error saving line items: ${errorMsg}`, 'error');
                self.elements.$saveLineItemsBtn.prop('disabled', false);
            },
            complete: function() {
                if (self.config.userType === 'lender') {
                    self.elements.$saveLineItemsBtn.text('Save');
                }
            }
        });
    },

    // ========================================================================
    //                      STATE MODIFICATION & UI ACTIONS
    // ========================================================================

    /**
     * Adds or updates a category in the state from the modal form, then re-renders.
     */
    saveCategoryFromModal: function() {
        if (!this.validateCategoryForm()) return;

        const categoryId = $('#modalCategoryId').val();
        const newCategoryData = {
            id: categoryId || `new_cat_${Date.now()}`,
            categoryName: $('#modalCategoryName').val().trim(),
            description: $('#modalCategoryDescription').val().trim(),
            lineItems: [] // New categories start with no line items
        };

        if (categoryId) { // Update existing
            const index = this.state.categories.findIndex(c => c.id == categoryId);
            if (index > -1) {
                // Preserve line items when updating
                newCategoryData.lineItems = this.state.categories[index].lineItems;
                this.state.categories[index] = newCategoryData;
            }
        } else { // Add new
            if (!this.checkAddMoreCategories(true)) return;
            this.state.categories.push(newCategoryData);
        }

        this.config.categoriesModified = true;
        this.renderCategoriesUI();
        $('#categoryFormModal').modal('hide');
    },

    /**
     * Removes a category from the state, then re-renders.
     * @param {number|string} categoryId - The ID of the category to delete.
     */
    deleteCategory: function(categoryId) {
        this.state.categories = this.state.categories.filter(c => c.id != categoryId);
        this.config.categoriesModified = true;
        this.renderCategoriesUI();
        this.checkAddMoreCategories();
    },

    /**
     * Adds a new, temporary line item to the state for a given category, then re-renders.
     * @param {number|string} categoryId - The ID of the category to add the line item to.
     */
    addNewLineItem: function(categoryId) {
        const category = this.state.categories.find(c => c.id == categoryId);
        if (!category) return;

        const newLineItem = {
            id: `new_li_${Date.now()}`,
            name: '', // Will be edited inline
            description: '',
            // Borrower/lender specific properties will be added as needed
            ...(this.config.userType === 'borrower' && { cost: 0, completedAmount: 0, completedPercent: 0, notes: '' })
        };

        if (!category.lineItems) category.lineItems = [];
        category.lineItems.push(newLineItem);

        this.config.lineItemsModified = true;
        this.renderLineItemsForCategoriesUI();

        // Focus the new row's input for immediate editing
        $(`tr[data-line-item-id="${newLineItem.id}"] .line-item-name-input`)
            .removeClass('d-none')
            .siblings('.line-item-display')
            .addClass('d-none')
            .end()
            .focus();
    },

    /**
     * Removes a line item from the state, then re-renders.
     * @param {number|string} categoryId - The ID of the parent category.
     * @param {number|string} lineItemId - The ID of the line item to delete.
     */
    removeLineItem: function(categoryId, lineItemId) {
        const category = this.state.categories.find(c => c.id == categoryId);
        if (category && category.lineItems) {
            category.lineItems = category.lineItems.filter(li => li.id != lineItemId);
            this.config.lineItemsModified = true;
            this.renderLineItemsForCategoriesUI();
        }
    },

    /**
     * Updates a line item in the state after an inline edit, then re-renders the specific field.
     * @param {jQuery} $input - The input element that was edited.
     */
    saveLineItemInlineEdit: function($input) {
        const $row = $input.closest('tr');
        const lineItemId = $row.data('line-item-id');
        const categoryId = $row.closest('.line-item-category-section').data('category-id');
        const field = $input.hasClass('line-item-name-input') ? 'name' : 'description';
        const newValue = $input.val().trim();

        const category = this.state.categories.find(c => c.id == categoryId);
        if (!category || !category.lineItems) return;
        const lineItem = category.lineItems.find(li => li.id == lineItemId);
        if (!lineItem) return;

        // If a new item's name is left blank, remove it.
        if (field === 'name' && newValue === '' && typeof lineItem.id === 'string' && lineItem.id.startsWith('new_')) {
            this.removeLineItem(categoryId, lineItem.id);
            return;
        }

        // Update the state
        lineItem[field] = newValue;
        this.config.lineItemsModified = true;
        this.elements.$saveLineItemsBtn.prop('disabled', false);

        // Update the view (revert to display state)
        const $display = $input.siblings('.line-item-display');
        if ($display.length) {
            $display.text(newValue).removeClass('d-none');
            $input.addClass('d-none');
        }
    },

    // ========================================================================
    //                          UI RENDERING
    // ========================================================================

    /**
     * Renders the category list based on the current state.
     */
    renderCategoriesUI: function() {
        const categoriesData = this.state.categories || [];
        const template = document.getElementById('category-item-template');
        this.elements.$categoriesContainer.empty();
        const categories = Array.isArray(categoriesData) ? categoriesData : Object.values(categoriesData);

        if (categories.length === 0) {
            this.elements.$categoriesContainer.append('<p class="no-cat">No categories found. Click "Add Category" to begin.</p>');
        } else {
            // Re-order based on index before rendering to respect drag-and-drop
            categories.forEach((category, index) => category.order = index + 1);

            categories.forEach(category => {
                const clone = template.content.cloneNode(true);
                const $item = $(clone).find('.category-item');
                $item.attr({
                    'data-category-id': category.id,
                    'data-name': category.categoryName,
                    'data-description': category.description
                });
                $item.find('.category-name').text(category.categoryName);
                $item.find('.category-description').text(category.description);
                this.elements.$categoriesContainer.append($item);
            });
        }
        this.elements.$saveCatBtn.prop('disabled', !this.config.categoriesModified);
    },

    /**
     * Renders all category cards and their respective line item tables based on the current state.
     */
    renderLineItemsForCategoriesUI: function() {
        const categoriesData = this.state.categories || [];
        this.elements.$lineItemCategoriesContainer.empty();
        const categories = Array.isArray(categoriesData) ? categoriesData : Object.values(categoriesData);

        if (categories.length === 0) {
            this.elements.$lineItemCategoriesContainer.html('<p class="text-muted text-center mt-5">No categories defined. Go to the "Categories" step to add them.</p>');
            return;
        }

        const cardTemplate = document.getElementById('line-item-category-card-template');
        categories.forEach((category, index) => {
            const clone = cardTemplate.content.cloneNode(true);
            const $card = $(clone).find('.line-item-category-section');
            const collapseId = `collapseCategory_${category.id}`;

            $card.attr('data-category-id', category.id);
            $card.find('.category-name').text(category.categoryName.toUpperCase());
            $card.find('.line-item-category-header').attr('data-target', `#${collapseId}`);
            $card.find('.category-collapse-placeholder').attr('id', collapseId);

            // Keep first 5 categories expanded by default
            if (index < 5) {
                $card.find('.category-collapse-placeholder').addClass('show');
                $card.find('.line-item-category-header').attr('aria-expanded', 'true');
            }

            this.elements.$lineItemCategoriesContainer.append($card);
            // Delegate the actual row rendering to the user-specific module
            this[this.config.userType].renderLineItemRowsUI(category.lineItems, $card.find('.line-items-tbody'));
        });

        this.initSortableLineItems();
        this.elements.$saveLineItemsBtn.prop('disabled', !this.config.lineItemsModified);
    },

    // ... The rest of the common functions like initCommonEventHandlers, initSortable, initStepper, modals, etc. ...
    // ... These functions will be modified to use the new state-first approach ...

    /**
     * Initializes all common event handlers.
     */
    initCommonEventHandlers: function() {
        const self = this;

        // --- Category Management ---
        self.elements.$openAddCategoryModalBtn.on('click', () => self.openCategoryModal('add'));
        self.elements.$saveCategoryModalBtn.on('click', () => self.saveCategoryFromModal());
        self.elements.$saveCatBtn.on('click', function() { self.saveCategories(this); });

        self.elements.$categoriesContainer.on('click', '.edit-category-btn', function(e) {
            e.preventDefault();
            const $item = $(this).closest('.category-item');
            self.openCategoryModal('edit', {
                id: $item.data('category-id'),
                name: $item.data('name'),
                description: $item.data('description')
            });
        });

        self.elements.$categoriesContainer.on('click', '.delete-category-btn', async function(e) {
            e.preventDefault();
            const categoryId = $(this).closest('.category-item').data('category-id');
            const confirm = await self.showConfirmationModal('Are you sure you want to delete this category and all its line items?');
            if (confirm) {
                self.deleteCategory(categoryId);
            }
        });

        // --- Line Item Management ---
        self.elements.$lineItemCategoriesContainer.on('click', '.add-new-line-item-link', function(e) {
            e.preventDefault();
            const categoryId = $(this).closest('.add-line-item-row').data('category-id');
            self.addNewLineItem(categoryId);
        });

        self.elements.$lineItemCategoriesContainer.on('click', '.remove-line-item-btn', async function(e) {
            e.preventDefault();
            const $row = $(this).closest('tr');
            const categoryId = $row.closest('.line-item-category-section').data('category-id');
            const lineItemId = $row.data('line-item-id');
            const confirm = await self.showConfirmationModal('Are you sure you want to delete this line item?');
            if (confirm) {
                self.removeLineItem(categoryId, lineItemId);
            }
        });

        self.elements.$lineItemCategoriesContainer.on('click', '.editable-line-item td:not(:last-child):not(:has(input[type=number]))', function() {
            $(this).find('.line-item-display').addClass('d-none');
            $(this).find('.line-item-input').removeClass('d-none').focus();
        });

        self.elements.$lineItemCategoriesContainer.on('blur', '.line-item-input', function(e) {
            self.saveLineItemInlineEdit($(this));
        });

        self.elements.$lineItemCategoriesContainer.on('keypress', '.line-item-input', function(e) {
            if (e.which === 13) { // Enter key
                $(this).blur();
            }
        });

        self.elements.$saveLineItemsBtn.on('click', async function() {
            self.config.isDraft = $(this).hasClass('save-draft');
            const confirmMsg = self.config.userType === 'lender'
                ? 'Are you sure you want to save these line items?'
                : (self.config.isDraft ? 'Save this Scope of Work as a draft?' : 'Are you sure you want to submit the Scope of Work?');

            const confirm = await self.showConfirmationModal(confirmMsg);
            if (confirm) self.saveLineItems();
            self.config.isDraft = false; // Reset after use
        });
    },

    // ... [Other helper functions like modals, stepper, sortable remain largely the same, but Sortable onEnd needs to update the state] ...

    /**
     * Initializes SortableJS for categories and line items.
     */
    initSortable: function() {
        const self = this;

        // Category Sorting
        new Sortable(this.elements.$categoriesContainer[0], {
            animation: 150,
            onEnd: function(evt) {
                const item = self.state.categories.splice(evt.oldIndex, 1)[0];
                self.state.categories.splice(evt.newIndex, 0, item);
                self.config.categoriesModified = true;
                self.elements.$saveCatBtn.prop('disabled', false);
            }
        });
    },

    initSortableLineItems: function() {
        const self = this;
        document.querySelectorAll('#lineItemCategoriesContainer .sortable').forEach(el => {
            new Sortable(el, {
                animation: 150,
                onEnd: function(evt) {
                    const categoryId = $(evt.from).closest('.line-item-category-section').data('category-id');
                    const category = self.state.categories.find(c => c.id == categoryId);
                    if (category && category.lineItems) {
                        const item = category.lineItems.splice(evt.oldIndex, 1)[0];
                        category.lineItems.splice(evt.newIndex, 0, item);
                        self.config.lineItemsModified = true;
                        self.elements.$saveLineItemsBtn.prop('disabled', false);
                    }
                }
            });
        });
    },

    /**
     * Initializes stepper functionality and handles navigation warnings.
     */
    initStepper: function() {
        const self = this;
        $('.step, .switch-step').on('click', async function() {
            const targetStepName = $(this).data('step') || $(this).data('target-step');
            if ($(`.step[data-step="${targetStepName}"]`).hasClass('active')) return;

            if (self.config.categoriesModified || self.config.lineItemsModified) {
                const confirm = await self.showConfirmationModal(
                    'You have unsaved changes. Are you sure you want to leave without saving? Your changes will be lost.'
                );
                if (!confirm) return;
            }
            self.switchStep(targetStepName, true); // Force switch without re-checking
        });
    },

    switchStep: function(targetStepName, force = false) {
        if (!force && ($(`.step[data-step="${targetStepName}"]`).hasClass('active'))) return;

        // Revert state if leaving a dirty form
        if (this.config.categoriesModified || this.config.lineItemsModified) {
            this.state = JSON.parse(JSON.stringify(this.lastSavedState));
        }

        $('.step').removeClass('active');
        $(`.step[data-step="${targetStepName}"]`).addClass('active');

        $('.step-content > div').hide();
        $(`#content-step-${targetStepName}`).show();

        // Re-render to ensure view is consistent with state after potential revert
        this.renderAll();
    },

    // --- [The rest of the utility functions like modals and validation are here] ---
    // These remain largely unchanged as they are helper utilities.
    showConfirmationModal: function(message) { /* ... implementation ... */ },
    openCategoryModal: function(mode, data = {}) { /* ... implementation ... */ },
    validateCategoryForm: function() { /* ... implementation ... */ },
    checkAddMoreCategories: function(showAlert = false) { /* ... implementation ... */ },
    buildCategoriesJson: function() {
        // This is now state-driven
        const categoriesToSave = this.state.categories.map((category, index) => {
            const id = (typeof category.id === 'string' && category.id.startsWith('new_')) ? null : category.id;
            return {
                id: id,
                categoryName: category.categoryName,
                description: category.description,
                order: index + 1 // Ensure order is always correct
            };
        });

        return {
            [this.config.dataKey]: this.config[this.config.dataKey],
            categories: categoriesToSave
        };
    }
};
