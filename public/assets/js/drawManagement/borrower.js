/**
 * Borrower-specific Draw Management Functionality
 *
 * This file manages the borrower's interaction with the Scope of Work and Draw Request forms.
 * It operates on a central `DrawManagement.state` object, which is the single source of truth for all data.
 * UI elements are rendered from this state, and user interactions update the state, which may trigger a re-render.
 */
DrawManagement.borrower = {
    /**
     * Initialize borrower-specific configuration and event handlers.
     */
    init: function() {
        DrawManagement.config.lmrid = $('#lmrid').val();
        DrawManagement.config.dataKey = 'lmrid';
        DrawManagement.config.dataUrl = `/backoffice/api_v2/draw_management/borrower/SowCategories?lmrid=${DrawManagement.config.lmrid}`;
        DrawManagement.config.saveLineItemsSuccessMessage = 'Draw Request Submitted!';
        this.initEventHandlers();
    },

    /**
     * Checks if the borrower has permission for an action based on settings in the state object.
     * @param {string} permission - The permission key to check (e.g., 'allowBorrowersAddEditCategories').
     * @returns {boolean} - True if the permission is granted and the form is not in a read-only state.
     */
    hasPermission: function(permission) {
        // CORRECTED: Read permission directly from the central state object.
        // The state now contains keys like 'allowBorrowersAddEditCategories' from the backend.
        return DrawManagement.state[permission] === 1 && DrawManagement.state.status !== 'pending';
    },

    /**
     * Applies UI restrictions based on the borrower's permissions.
     * This function is called after the initial data is loaded.
     */
    applyPermissions: function() {
        const self = this;

        if (!self.hasPermission('allowBorrowersAddEditCategories')) {
            DrawManagement.elements.$openAddCategoryModalBtn.remove();
            $('.edit-category-btn').remove();
        }

        if (!self.hasPermission('allowBorrowersDeleteCategories')) {
            $('.delete-category-btn').remove();
        }

        if (!self.hasPermission('allowBorrowersAddEditLineItems')) {
            // Disable adding new line items and inline editing.
            $('.add-line-item-row').remove();
            DrawManagement.elements.$lineItemCategoriesContainer.off('click', '.editable-line-item td:not(:last-child)');
        }

        if (!self.hasPermission('allowBorrowersDeleteLineItems')) {
            $('.col-action').remove(); // Removes the delete column header and buttons.
        }

        // Disable save buttons if no modification permissions are granted.
        if (!self.hasPermission('allowBorrowersAddEditCategories') && !self.hasPermission('allowBorrowersDeleteCategories')) {
            DrawManagement.elements.$saveCatBtn.remove();
        }

        // If the entire request is pending, disable all inputs and remove save buttons.
        if (DrawManagement.state.status === 'pending') {
            DrawManagement.elements.$lineItemCategoriesContainer.find('input[type="number"]').prop('disabled', true);
            DrawManagement.elements.$saveLineItemsBtn.remove();
        }
    },

    /**
     * Initializes all borrower-specific event handlers.
     */
    initEventHandlers: function() {
        this.initCalculationHandlers();
        this.initNotesModal();
        this.initNotesPopover('.note-btn'); // Initialize popovers for all note buttons
    },

    /**
     * Sets up event handlers for line item cost and percentage calculations.
     * All calculations update the central state object first, then the UI.
     */
    initCalculationHandlers: function() {
        const self = this;
        DrawManagement.elements.$lineItemCategoriesContainer.on('input', 'input[type="number"]', function() {
            const $input = $(this);
            const $row = $input.closest('tr');
            const lineItemId = parseInt($row.data('line-item-id'), 10);
            const categoryId = parseInt($row.closest('.line-item-category-section').data('category-id'), 10);
            const fieldName = $input.attr('name');

            // Find the corresponding line item in the state.
            const category = DrawManagement.state.categories.find(c => c.id === categoryId);
            if (!category) return;
            const lineItem = category.lineItems.find(li => li.id === lineItemId);
            if (!lineItem) return;

            let value = parseFloat($input.val()) || 0;

            // --- Update State Object ---
            if (fieldName === 'cost') {
                lineItem.cost = value < 0 ? 0 : value;
                lineItem.completedAmount = (lineItem.completedPercent / 100) * lineItem.cost;
            } else if (fieldName === 'completedAmount') {
                lineItem.completedAmount = Math.max(0, Math.min(value, lineItem.cost));
                lineItem.completedPercent = lineItem.cost > 0 ? (lineItem.completedAmount / lineItem.cost) * 100 : 0;
            } else if (fieldName === 'completedPercent') {
                lineItem.completedPercent = Math.max(0, Math.min(value, 100));
                lineItem.completedAmount = (lineItem.completedPercent / 100) * lineItem.cost;
            }

            // --- Update UI from State ---
            // This ensures the UI always reflects the validated and calculated state.
            $row.find('input[name="cost"]').val(lineItem.cost.toFixed(2));
            $row.find('input[name="completedAmount"]').val(lineItem.completedAmount.toFixed(2));
            $row.find('input[name="completedPercent"]').val(lineItem.completedPercent.toFixed(2));

            DrawManagement.config.lineItemsModified = true;
            DrawManagement.elements.$saveLineItemsBtn.prop('disabled', false);
        });
    },

    /**
     * Initializes the modal used for editing borrower notes and descriptions.
     */
    initNotesModal: function() {
        let currentLineItemId, currentCategoryId, noteType;

        $('#noteModal').on('show.bs.modal', function(event) {
            const $button = $(event.relatedTarget);
            const $row = $button.closest('tr');
            currentLineItemId = parseInt($row.data('line-item-id'), 10);
            currentCategoryId = parseInt($row.closest('.line-item-category-section').data('category-id'), 10);
            noteType = $button.hasClass('description-btn') ? 'description' : 'notes';

            const noteText = $button.data('note') || '';
            $('#noteTextarea').val(noteText);
        });

        $('#saveNoteBtn').on('click', function() {
            // Find the item in state and update it.
            const category = DrawManagement.state.categories.find(c => c.id === currentCategoryId);
            if (!category) return;
            const lineItem = category.lineItems.find(li => li.id === currentLineItemId);
            if (!lineItem) return;

            const updatedNote = $('#noteTextarea').val();
            lineItem[noteType] = updatedNote;

            // Update the data attribute on the button for the next open.
            const $button = $(`tr[data-line-item-id="${currentLineItemId}"] .note-btn` + (noteType === 'description' ? '.description-btn' : ':not(.description-btn)'));
            $button.data('note', updatedNote).attr('data-note', updatedNote);

            $('#noteModal').modal('hide');
            DrawManagement.config.lineItemsModified = true;
            DrawManagement.elements.$saveLineItemsBtn.prop('disabled', false);
        });
    },

    /**
     * Initializes hover-to-show popovers for notes.
     * @param {string} elementSelector - The selector for the button that triggers the popover.
     */
    initNotesPopover: function(elementSelector) {
        DrawManagement.elements.$lineItemCategoriesContainer.on('mouseenter', elementSelector, function() {
            const $btn = $(this);
            const note = $btn.data('note');
            const $popover = $btn.siblings('.popover');

            if (note && note.trim()) {
                $popover.text(note).stop(true, true).fadeIn(100);
            }
        }).on('mouseleave', elementSelector, function() {
            $(this).siblings('.popover').stop(true, true).fadeOut(100);
        });
    },

    /**
     * Builds the JSON payload for saving line items by reading from the state object.
     * This is more robust than scraping the DOM.
     * @returns {object} - The payload for the API request.
     */
    buildLineItemsJson: function() {
        const groupedLineItems = {};

        // Process the state object to build the payload.
        DrawManagement.state.categories.forEach(category => {
            // Ensure even categories with no line items are included if they exist in the state.
            groupedLineItems[category.id] = category.lineItems || [];
        });

        return {
            lmrid: DrawManagement.config.lmrid,
            lineItems: groupedLineItems,
            isDraft: DrawManagement.config.isDraft
        };
    },

    /**
     * Renders the UI for a list of line items within a specific category.
     * @param {Array} lineItems - An array of line item objects from the state.
     * @param {jQuery} $tbodyElement - The jQuery object for the table body to append rows to.
     */
    renderLineItemRowsUI: function(lineItems, $tbodyElement) {
        const rowTemplate = document.getElementById('line-item-row-template');
        if (!rowTemplate) {
            console.error("line-item-row-template not found!");
            return;
        }

        if (lineItems && lineItems.length > 0) {
            lineItems.forEach(item => {
                const clone = rowTemplate.content.cloneNode(true);
                const $row = $(clone).find('tr');
                $row.attr('data-line-item-id', item.id);

                // Set display text and input values from the item object.
                $row.find('.line-item-name-display').text(item.name);
                $row.find('.line-item-name-input').val(item.name);

                // Highlight rejected items
                if (DrawManagement.state.status === 'rejected' && item.rejectReason) {
                    $row.addClass('table-danger').attr('title', `Rejection Reason: ${item.rejectReason}`);
                }

                $row.find('input[name="cost"]').val(parseFloat(item.cost || 0).toFixed(2));
                $row.find('input[name="completedAmount"]').val(parseFloat(item.completedAmount || 0).toFixed(2));
                $row.find('input[name="completedPercent"]').val(parseFloat(item.completedPercent || 0).toFixed(2));

                // Set data attributes for notes, making them available to the popover and modal.
                $row.find('.note-btn:not(.description-btn)').data('note', item.notes || '').attr('data-note', item.notes || '');
                $row.find('.description-btn').data('note', item.description || '').attr('data-note', item.description || '');

                let lenderNotesText = item.lenderNotes || '';
                if (item.rejectReason) {
                    lenderNotesText += (lenderNotesText ? '\n\n' : '') + `Rejection Reason: ${item.rejectReason}`;
                }
                $row.find('.lender-notes').data('note', lenderNotesText).attr('data-note', lenderNotesText);

                $tbodyElement.append($row);
            });
        }
    }
};
